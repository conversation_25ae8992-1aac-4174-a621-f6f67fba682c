"""
基于多种方法的高精度人脸检测器
"""

import cv2
import numpy as np
import torch
import json
import os
from pathlib import Path
import logging
from typing import List, Tuple, Optional

# 尝试导入MTCNN
try:
    from mtcnn import MTCNN
    MTCNN_AVAILABLE = True
except ImportError:
    MTCNN_AVAILABLE = False


# 尝试导入MediaPipe
try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
except ImportError:
    MEDIAPIPE_AVAILABLE = False

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedFaceDetector:
    """高精度人脸检测器"""

    def __init__(self, confidence_threshold: float = 0.9):
        """初始化人脸检测器"""
        self.confidence_threshold = confidence_threshold
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"使用设备: {self.device}")
        logger.info(f"人脸检测置信度阈值: {confidence_threshold}")

        # 初始化检测器
        self.detectors = []
        self._init_detectors()

        if not self.detectors:
            raise RuntimeError("没有可用的高精度人脸检测器。请安装 mtcnn 或 mediapipe:\n"
                             "pip install mtcnn mediapipe")

    def _init_detectors(self):
        """初始化所有可用的检测器"""

        # 1. 优先初始化MTCNN（高精度人脸检测）
        if MTCNN_AVAILABLE:
            try:
                # 直接传递字符串
                device_name = 'cuda' if torch.cuda.is_available() else 'cpu'

                # 使用基本参数初始化MTCNN
                self.mtcnn = MTCNN(device=device_name)
                self.detectors.append('mtcnn')
                logger.info("MTCNN人脸检测器初始化成功")
                logger.info(f"MTCNN参数: device={device_name}")
            except Exception as e:
                logger.error(f"MTCNN初始化失败: {e}")

        # 2. 初始化MediaPipe作为备用（如果MTCNN不可用）
        if not self.detectors and MEDIAPIPE_AVAILABLE:
            try:
                self.mp_face_detection = mp.solutions.face_detection
                self.mp_drawing = mp.solutions.drawing_utils
                self.face_detection = self.mp_face_detection.FaceDetection(
                    model_selection=1, 
                    min_detection_confidence=0.7  # 检测置信度
                )
                self.detectors.append('mediapipe')
                logger.info("MediaPipe人脸检测器初始化成功（备用）")
            except Exception as e:
                logger.warning(f"MediaPipe初始化失败: {e}")

        # 如果所有高精度检测器都失败，抛出错误
        if not self.detectors:
            raise RuntimeError("没有可用的高精度人脸检测器。请安装 mtcnn 或 mediapipe:\n"
                             "pip install mtcnn mediapipe")

        logger.info(f"可用的人脸检测器: {self.detectors}")

    def detect_faces_mtcnn(self, image: np.ndarray, image_name: str = "unknown") -> List[Tuple[int, int, int, int, float]]:
        """使用MTCNN检测人脸"""
        if 'mtcnn' not in self.detectors:
            logger.debug(f"MTCNN不可用: {image_name}")
            return []

        try:
            # 检查图像有效性
            if image is None or image.size == 0:
                logger.debug(f"图像无效或为空: {image_name}")
                return []

            # 检查图像尺寸
            if len(image.shape) != 3 or image.shape[2] != 3:
                logger.debug(f"图像格式不正确: {image.shape}, 文件: {image_name}")
                return []

            # 检查图像最小尺寸
            if image.shape[0] < 20 or image.shape[1] < 20:
                logger.debug(f"图像尺寸太小: {image.shape}, 文件: {image_name}")
                return []

            logger.debug(f"开始MTCNN检测: {image_name}, 尺寸: {image.shape}")

            # MTCNN需要RGB格式
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

            # 检测人脸
            result = self.mtcnn.detect_faces(rgb_image)

            logger.debug(f"MTCNN原始检测结果: {len(result) if result else 0} 个候选人脸, 文件: {image_name}")

            faces = []
            if result and len(result) > 0:
                for i, face in enumerate(result):
                    confidence = face['confidence']
                    logger.debug(f"候选人脸 {i+1}: 置信度={confidence:.3f}, 阈值={self.confidence_threshold}")

                    if confidence >= self.confidence_threshold:
                        box = face['box']
                        x, y, w, h = box

                        # 确保坐标为正数且在图像范围内
                        x, y = max(0, x), max(0, y)
                        w = min(w, image.shape[1] - x)
                        h = min(h, image.shape[0] - y)

                        # 确保检测框有效
                        if w > 10 and h > 10:
                            faces.append((x, y, w, h, confidence))
                            logger.debug(f"接受人脸 {i+1}: 位置=({x},{y},{w},{h}), 置信度={confidence:.3f}")
                        else:
                            logger.debug(f"拒绝人脸 {i+1}: 尺寸太小 ({w}x{h})")
                    else:
                        logger.debug(f"拒绝人脸 {i+1}: 置信度太低 ({confidence:.3f} < {self.confidence_threshold})")
            else:
                logger.debug(f"MTCNN未检测到人脸: {image_name}")

            logger.debug(f"MTCNN最终结果: {len(faces)} 个有效人脸, 文件: {image_name}")
            return faces

        except Exception as e:
            logger.warning(f"MTCNN人脸检测异常: {e}, 文件: {image_name}")
            return []

    def detect_faces_mediapipe(self, image: np.ndarray, image_name: str = "unknown") -> List[Tuple[int, int, int, int, float]]:
        """使用MediaPipe检测人脸"""
        if 'mediapipe' not in self.detectors:
            return []

        try:
            # MediaPipe需要RGB格式
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

            # 检测人脸
            results = self.face_detection.process(rgb_image)

            faces = []
            if results.detections:
                h, w, _ = image.shape
                logger.debug(f"MediaPipe检测到 {len(results.detections)} 个候选人脸: {image_name}")

                for i, detection in enumerate(results.detections):
                    confidence = detection.score[0]
                    logger.debug(f"MediaPipe候选人脸 {i+1}: 置信度={confidence:.3f}, 阈值={self.confidence_threshold}")

                    if confidence >= self.confidence_threshold:
                        bbox = detection.location_data.relative_bounding_box
                        x = int(bbox.xmin * w)
                        y = int(bbox.ymin * h)
                        width = int(bbox.width * w)
                        height = int(bbox.height * h)

                        # 确保坐标在图像范围内
                        x, y = max(0, x), max(0, y)
                        width = min(width, w - x)
                        height = min(height, h - y)

                        # 确保检测框有效
                        if width > 20 and height > 20:
                            faces.append((x, y, width, height, confidence))
                            logger.debug(f"MediaPipe接受人脸 {i+1}: 位置=({x},{y},{width},{height}), 置信度={confidence:.3f}")
                        else:
                            logger.debug(f"MediaPipe拒绝人脸 {i+1}: 尺寸太小 ({width}x{height})")
                    else:
                        logger.debug(f"MediaPipe拒绝人脸 {i+1}: 置信度太低 ({confidence:.3f} < {self.confidence_threshold})")
            else:
                logger.debug(f"MediaPipe未检测到人脸: {image_name}")

            logger.debug(f"MediaPipe最终结果: {len(faces)} 个有效人脸, 文件: {image_name}")
            return faces

        except Exception as e:
            logger.warning(f"MediaPipe人脸检测异常: {e}, 文件: {image_name}")
            return []

    def detect_faces(self, image: np.ndarray, image_name: str = "unknown") -> List[Tuple[int, int, int, int, float]]:
        """检测人脸 - 按优先级尝试不同的检测器"""

        logger.debug(f"开始人脸检测: {image_name}")

        # 按优先级尝试检测器
        for detector_name in self.detectors:
            logger.debug(f"尝试使用 {detector_name} 检测器: {image_name}")
            faces = []

            if detector_name == 'mtcnn':
                faces = self.detect_faces_mtcnn(image, image_name)
            elif detector_name == 'mediapipe':
                faces = self.detect_faces_mediapipe(image, image_name)

            if faces:
                logger.info(f"{detector_name}成功检测到 {len(faces)} 个人脸: {image_name}")
                return faces
            else:
                logger.debug(f"{detector_name}未检测到人脸: {image_name}")

        logger.info(f"所有检测器均未检测到符合要求的人脸: {image_name}")
        return []
    
    def crop_face(self, image: np.ndarray, bbox: Tuple[int, int, int, int], 
                  target_size: Tuple[int, int] = (160, 160)) -> Optional[np.ndarray]:
        """裁剪人脸区域"""
        x, y, w, h = bbox[:4]
        
        # 确保边界框在图像范围内
        x = max(0, x)
        y = max(0, y)
        w = min(w, image.shape[1] - x)
        h = min(h, image.shape[0] - y)
        
        if w <= 0 or h <= 0:
            return None
        
        # 裁剪人脸
        face = image[y:y+h, x:x+w]
        
        # 调整大小
        if face.size > 0:
            face_resized = cv2.resize(face, target_size)
            return face_resized
        
        return None
    
    def process_image(self, image_path: str, output_path: str) -> bool:
        """处理单张图片"""
        image_name = os.path.basename(image_path)

        try:
            logger.info(f"开始处理图片: {image_name}")

            # 读取图片
            image = cv2.imread(image_path)
            if image is None:
                logger.error(f"无法读取图片: {image_name}")
                return False

            logger.debug(f"图片读取成功: {image_name}, 尺寸: {image.shape}")

            # 检测人脸
            faces = self.detect_faces(image, image_name)

            if not faces:
                logger.info(f"未检测到符合要求的人脸: {image_name}")
                return False

            # 选择置信度最高的人脸
            best_face = max(faces, key=lambda f: f[4])
            logger.info(f"选择最佳人脸: {image_name}, 置信度={best_face[4]:.3f}, 位置=({best_face[0]}, {best_face[1]}, {best_face[2]}, {best_face[3]})")

            # 裁剪人脸
            face_crop = self.crop_face(image, best_face)

            if face_crop is None:
                logger.error(f"人脸裁剪失败: {image_name}")
                return False

            # 验证裁剪结果
            if face_crop.shape[0] < 20 or face_crop.shape[1] < 20:
                logger.error(f"裁剪的人脸太小: {face_crop.shape}, 文件: {image_name}")
                return False

            # 保存
            success = cv2.imwrite(output_path, face_crop)

            if success:
                logger.info(f"成功处理: {image_name} -> {face_crop.shape}, 保存到: {os.path.basename(output_path)}")
                return True
            else:
                logger.error(f"保存失败: {image_name} -> {output_path}")
                return False

        except Exception as e:
            logger.error(f"处理图片时出错: {image_name}, 错误: {e}")
            return False

class FaceDatasetBuilder:
    """人脸数据集构建器"""

    def __init__(self, output_dir: str = "face_dataset", confidence_threshold: float = 0.9):
        """初始化数据集构建器"""
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

        # 创建子目录
        self.before_dir = self.output_dir / "before"
        self.after_dir = self.output_dir / "after"
        self.before_dir.mkdir(exist_ok=True)
        self.after_dir.mkdir(exist_ok=True)

        # 初始化高精度人脸检测器
        self.detector = AdvancedFaceDetector(confidence_threshold=confidence_threshold)

        # 统计信息
        self.stats = {
            "total_processed": 0,
            "successful_before": 0,
            "successful_after": 0,
            "failed_before": 0,
            "failed_after": 0,
            "valid_pairs": 0,
            "low_confidence_faces": 0
        }

        logger.info(f"数据集构建器初始化完成，输出目录: {self.output_dir}")
        logger.info(f"人脸检测置信度阈值: {confidence_threshold}")
    
    def build_dataset(self, summary_json_path: str = "summary.json") -> dict:
        """构建人脸数据集"""
        logger.info("开始构建人脸数据集")

        # 读取summary.json
        try:
            with open(summary_json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            logger.error(f"无法读取 {summary_json_path}: {e}")
            return {}

        dataset = []
        skipped_invalid_bmi = 0

        for i, item in enumerate(data):
            self.stats["total_processed"] += 1

            # 检查BMI数据有效性
            start_bmi = item.get("start_bmi")
            end_bmi = item.get("end_bmi")

            # 跳过包含None/null值的记录
            if start_bmi is None or end_bmi is None:
                logger.debug(f"跳过BMI数据无效的记录: {item.get('before_image_path', 'unknown')}")
                skipped_invalid_bmi += 1
                continue

            # 尝试转换为float，如果失败则跳过
            try:
                start_bmi_float = float(start_bmi)
                end_bmi_float = float(end_bmi)

                # 检查BMI值是否合理（5-200范围）
                if not (5 <= start_bmi_float <= 200) or not (5 <= end_bmi_float <= 200):
                    logger.debug(f"跳过BMI值异常的记录: start={start_bmi_float}, end={end_bmi_float}")
                    skipped_invalid_bmi += 1
                    continue

            except (ValueError, TypeError) as e:
                logger.debug(f"跳过BMI转换失败的记录: {e}")
                skipped_invalid_bmi += 1
                continue

            # 获取图片路径
            before_path = item.get("before_image_path", "").replace("\\", "/")
            after_path = item.get("after_image_path", "").replace("\\", "/")

            if not before_path or not after_path:
                logger.debug(f"跳过图片路径缺失的记录")
                continue

            # 生成输出文件名
            base_name = Path(before_path).stem.replace("_before", "")
            before_output = self.before_dir / f"{base_name}.jpg"
            after_output = self.after_dir / f"{base_name}.jpg"

            # 处理before图片
            before_success = False
            if os.path.exists(before_path):
                before_success = self.detector.process_image(before_path, str(before_output))
                if before_success:
                    self.stats["successful_before"] += 1
                else:
                    self.stats["failed_before"] += 1
            else:
                logger.debug(f"Before图片不存在: {before_path}")
                self.stats["failed_before"] += 1

            # 处理after图片
            after_success = False
            if os.path.exists(after_path):
                after_success = self.detector.process_image(after_path, str(after_output))
                if after_success:
                    self.stats["successful_after"] += 1
                else:
                    self.stats["failed_after"] += 1
            else:
                logger.debug(f"After图片不存在: {after_path}")
                self.stats["failed_after"] += 1

            # 如果两张图片都成功处理
            if before_success and after_success:
                self.stats["valid_pairs"] += 1

                dataset_item = {
                    "before_image_path": str(before_output.relative_to(self.output_dir)),
                    "after_image_path": str(after_output.relative_to(self.output_dir)),
                    "start_bmi": start_bmi_float,
                    "end_bmi": end_bmi_float
                }
                dataset.append(dataset_item)

            # 每100个样本输出一次进度
            if (i + 1) % 100 == 0:
                logger.info(f"已处理: {i + 1}/{len(data)}, 有效样本: {self.stats['valid_pairs']}, 跳过无效BMI: {skipped_invalid_bmi}")

        # 保存数据集
        dataset_path = self.output_dir / "face_dataset.json"
        with open(dataset_path, 'w', encoding='utf-8') as f:
            json.dump(dataset, f, indent=2, ensure_ascii=False)

        # 打印统计信息
        self._print_statistics()
        logger.info(f"跳过无效BMI记录: {skipped_invalid_bmi}")

        logger.info(f"数据集构建完成，保存到: {dataset_path}")
        logger.info(f"有效样本数: {len(dataset)}")

        return {
            "dataset": dataset,
            "dataset_path": str(dataset_path),
            "statistics": self.stats,
            "skipped_invalid_bmi": skipped_invalid_bmi
        }
    
    def _print_statistics(self):
        """打印统计信息"""
        logger.info("处理统计:")
        logger.info(f"  总处理数量: {self.stats['total_processed']}")
        logger.info(f"  Before成功: {self.stats['successful_before']}")
        logger.info(f"  After成功: {self.stats['successful_after']}")
        logger.info(f"  Before失败: {self.stats['failed_before']}")
        logger.info(f"  After失败: {self.stats['failed_after']}")
        logger.info(f"  有效样本对: {self.stats['valid_pairs']}")
        
        total_images = self.stats['total_processed'] * 2
        successful_images = self.stats['successful_before'] + self.stats['successful_after']
        success_rate = successful_images / total_images * 100 if total_images > 0 else 0
        logger.info(f"  成功率: {success_rate:.1f}%")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="高精度人脸检测数据集构建")
    parser.add_argument("--input", "-i", default="summary.json", help="输入的summary.json文件")
    parser.add_argument("--output", "-o", default="face_dataset", help="输出目录")
    parser.add_argument("--confidence", "-c", type=float, default=0.8,
                       help="人脸检测置信度阈值 (0.0-1.0, 默认0.8)")

    args = parser.parse_args()

    # 验证置信度阈值
    if not 0.0 <= args.confidence <= 1.0:
        logger.error("置信度阈值必须在0.0到1.0之间")
        return

    # 检查输入文件
    if not os.path.exists(args.input):
        logger.error(f"输入文件不存在: {args.input}")
        return

    # 构建数据集
    builder = FaceDatasetBuilder(args.output, confidence_threshold=args.confidence)
    result = builder.build_dataset(args.input)

    if result:
        print(f"数据集构建完成")
        print(f"输出目录: {args.output}")
        print(f"有效样本数: {len(result['dataset'])}")
        print(f"数据集文件: {result['dataset_path']}")
        print(f"置信度阈值: {args.confidence}")
        if 'skipped_invalid_bmi' in result:
            print(f"跳过无效BMI记录: {result['skipped_invalid_bmi']}")
        if 'low_confidence_faces' in result['statistics']:
            print(f"低置信度人脸被过滤: {result['statistics']['low_confidence_faces']}")

if __name__ == "__main__":
    main()
