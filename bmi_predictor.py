"""
BMI预测推理器
"""

import torch
import torchvision.transforms as transforms
import cv2
import numpy as np
from pathlib import Path
import logging
from facenet_bmi_model import PairedBMIPredictor
from face_detector import AdvancedFaceDetector

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PairedBMIInference:
    """配对BMI预测推理器"""

    def __init__(self, model_path, confidence_threshold=0.9, use_pairing=False):
        """初始化推理器"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.use_pairing = use_pairing
        logger.info(f"使用设备: {self.device}")
        logger.info(f"推理模式: {'配对模式' if use_pairing else '单张模式'}")

        # 加载BMI预测模型
        self.bmi_model = PairedBMIPredictor(pretrained=False, use_pairing=use_pairing)

        # 加载模型状态，忽略不匹配的键
        state_dict = torch.load(model_path, map_location=self.device)
        model_dict = self.bmi_model.state_dict()

        # 过滤掉不匹配的键
        filtered_state_dict = {k: v for k, v in state_dict.items() if k in model_dict and model_dict[k].shape == v.shape}

        # 更新模型字典
        model_dict.update(filtered_state_dict)
        self.bmi_model.load_state_dict(model_dict)

        self.bmi_model.to(self.device)
        self.bmi_model.eval()
        logger.info(f"BMI模型加载完成: {model_path}")
        logger.info(f"成功加载 {len(filtered_state_dict)}/{len(state_dict)} 个参数")

        # 加载人脸检测器
        self.face_detector = AdvancedFaceDetector(confidence_threshold=confidence_threshold)
        logger.info("人脸检测器加载完成")

        # 数据预处理
        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((160, 160)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
        ])

    def predict_from_image(self, image_path):
        """从图片预测BMI"""
        try:
            # 读取图片
            image = cv2.imread(image_path)
            if image is None:
                logger.error(f"无法读取图片: {image_path}")
                return None
            
            # 检测人脸
            faces = self.face_detector.detect_faces(image)
            
            if not faces:
                logger.warning(f"未检测到人脸: {image_path}")
                return None
            
            # 选择置信度最高的人脸
            best_face = max(faces, key=lambda f: f[4])
            
            # 裁剪人脸
            face_crop = self.face_detector.crop_face(image, best_face)
            
            if face_crop is None:
                logger.error(f"人脸裁剪失败: {image_path}")
                return None
            
            # 预处理
            face_rgb = cv2.cvtColor(face_crop, cv2.COLOR_BGR2RGB)
            face_tensor = self.transform(face_rgb).unsqueeze(0).to(self.device)
            
            # 预测BMI
            with torch.no_grad():
                bmi_pred = self.bmi_model(face_tensor)
                bmi_value = bmi_pred.item()
            
            return bmi_value
            
        except Exception as e:
            logger.error(f"预测失败 {image_path}: {e}")
            return None
    
    def predict_from_face_crop(self, face_image):
        """从已裁剪的人脸图片预测BMI"""
        try:
            # 预处理
            if isinstance(face_image, str):
                face_image = cv2.imread(face_image)
                face_image = cv2.cvtColor(face_image, cv2.COLOR_BGR2RGB)
            elif len(face_image.shape) == 3 and face_image.shape[2] == 3:
                # BGR to RGB
                face_image = cv2.cvtColor(face_image, cv2.COLOR_BGR2RGB)

            face_tensor = self.transform(face_image).unsqueeze(0).to(self.device)

            # 预测BMI
            with torch.no_grad():
                if self.use_pairing:
                    # 配对模式下单张图片预测：使用start_bmi_head作为单张预测
                    features = self.bmi_model.facenet(face_tensor)
                    # 对于配对模型，我们需要通过特征融合层
                    # 将单张图片特征复制一份模拟配对
                    combined_features = torch.cat([features, features], dim=1)
                    fused_features = self.bmi_model.feature_fusion(combined_features)
                    relation_features = self.bmi_model.relation_layer(fused_features)
                    # 使用start_bmi_head进行预测
                    bmi_pred = self.bmi_model.start_bmi_head(relation_features)
                    bmi_value = bmi_pred.item()
                else:
                    bmi_pred = self.bmi_model(face_tensor)
                    bmi_value = bmi_pred.item()

            return bmi_value

        except Exception as e:
            logger.error(f"预测失败: {e}")
            return None

    def predict_from_pair(self, before_image_path, after_image_path):
        """从配对图片预测BMI"""
        if not self.use_pairing:
            logger.error("当前模型不支持配对预测")
            return None

        try:
            # 读取并处理before图片
            before_image = cv2.imread(before_image_path)
            if before_image is None:
                logger.error(f"无法读取before图片: {before_image_path}")
                return None

            # 检测before人脸
            before_faces = self.face_detector.detect_faces(before_image)
            if not before_faces:
                logger.warning(f"before图片未检测到人脸: {before_image_path}")
                return None

            before_face = self.face_detector.crop_face(before_image, max(before_faces, key=lambda f: f[4]))
            if before_face is None:
                return None

            # 读取并处理after图片
            after_image = cv2.imread(after_image_path)
            if after_image is None:
                logger.error(f"无法读取after图片: {after_image_path}")
                return None

            # 检测after人脸
            after_faces = self.face_detector.detect_faces(after_image)
            if not after_faces:
                logger.warning(f"after图片未检测到人脸: {after_image_path}")
                return None

            after_face = self.face_detector.crop_face(after_image, max(after_faces, key=lambda f: f[4]))
            if after_face is None:
                return None

            # 预处理
            before_rgb = cv2.cvtColor(before_face, cv2.COLOR_BGR2RGB)
            after_rgb = cv2.cvtColor(after_face, cv2.COLOR_BGR2RGB)

            before_tensor = self.transform(before_rgb).unsqueeze(0).to(self.device)
            after_tensor = self.transform(after_rgb).unsqueeze(0).to(self.device)

            # 配对预测
            with torch.no_grad():
                batch_data = {
                    'before_image': before_tensor,
                    'after_image': after_tensor
                }
                predictions = self.bmi_model(batch_data)

                return {
                    'start_bmi': predictions['start_bmi'].item(),
                    'end_bmi': predictions['end_bmi'].item(),
                    'bmi_change': predictions['bmi_change'].item()
                }

        except Exception as e:
            logger.error(f"配对预测失败: {e}")
            return None

    def predict_from_face_crop_pair(self, before_face_path, after_face_path):
        """从预裁剪的人脸图片进行配对预测"""
        if not self.use_pairing:
            logger.error("当前模型不支持配对预测")
            return None

        try:
            # 直接读取预裁剪的人脸图片
            before_face = cv2.imread(before_face_path)
            if before_face is None:
                logger.error(f"无法读取before人脸图片: {before_face_path}")
                return None

            after_face = cv2.imread(after_face_path)
            if after_face is None:
                logger.error(f"无法读取after人脸图片: {after_face_path}")
                return None

            # 预处理
            before_rgb = cv2.cvtColor(before_face, cv2.COLOR_BGR2RGB)
            after_rgb = cv2.cvtColor(after_face, cv2.COLOR_BGR2RGB)

            before_tensor = self.transform(before_rgb).unsqueeze(0).to(self.device)
            after_tensor = self.transform(after_rgb).unsqueeze(0).to(self.device)

            # 配对预测
            with torch.no_grad():
                batch_data = {
                    'before_image': before_tensor,
                    'after_image': after_tensor
                }
                predictions = self.bmi_model(batch_data)

                return {
                    'start_bmi': predictions['start_bmi'].item(),
                    'end_bmi': predictions['end_bmi'].item(),
                    'bmi_change': predictions['bmi_change'].item()
                }

        except Exception as e:
            logger.error(f"配对预测失败: {e}")
            return None

    def batch_predict(self, image_paths):
        """批量预测"""
        results = []
        
        for image_path in image_paths:
            bmi = self.predict_from_image(image_path)
            results.append({
                'image_path': image_path,
                'predicted_bmi': bmi
            })
        
        return results
    
    def get_bmi_category(self, bmi):
        """获取BMI分类"""
        if bmi < 18.5:
            return "underweight"
        elif bmi < 25:
            return "normal"
        elif bmi < 30:
            return "overweight"
        else:
            return "obese"

class BMIEvaluator:
    """BMI模型评估器"""

    def __init__(self, model_path, dataset_path, confidence_threshold=0.9, use_pairing=False):
        """初始化评估器"""
        self.predictor = PairedBMIInference(model_path, confidence_threshold=confidence_threshold, use_pairing=use_pairing)
        self.dataset_path = dataset_path
    
    def evaluate_dataset(self):
        """评估整个数据集"""
        import json
        from sklearn.metrics import mean_absolute_error, mean_squared_error
        
        logger.info("开始评估数据集")
        
        # 读取数据集
        with open(self.dataset_path, 'r', encoding='utf-8') as f:
            dataset = json.load(f)
        
        predictions = []
        targets = []
        
        dataset_dir = Path(self.dataset_path).parent
        
        for i, item in enumerate(dataset):
            # 评估before图片
            before_path = dataset_dir / item['before_image_path']
            if before_path.exists():
                pred_bmi = self.predictor.predict_from_face_crop(str(before_path))
                if pred_bmi is not None:
                    predictions.append(pred_bmi)
                    targets.append(item['start_bmi'])
            
            # 评估after图片
            after_path = dataset_dir / item['after_image_path']
            if after_path.exists():
                pred_bmi = self.predictor.predict_from_face_crop(str(after_path))
                if pred_bmi is not None:
                    predictions.append(pred_bmi)
                    targets.append(item['end_bmi'])
            
            if (i + 1) % 100 == 0:
                logger.info(f"已评估: {i + 1}/{len(dataset)}")
        
        # 计算指标
        mae = mean_absolute_error(targets, predictions)
        rmse = np.sqrt(mean_squared_error(targets, predictions))
        
        logger.info(f"评估完成")
        logger.info(f"样本数: {len(predictions)}")
        logger.info(f"MAE: {mae:.4f}")
        logger.info(f"RMSE: {rmse:.4f}")
        
        return {
            'mae': mae,
            'rmse': rmse,
            'num_samples': len(predictions),
            'predictions': predictions,
            'targets': targets
        }

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="BMI预测推理")
    parser.add_argument("--model", "-m", required=True, help="BMI模型路径")
    parser.add_argument("--image", "-i", help="输入图片路径")
    parser.add_argument("--dataset", "-d", help="数据集路径（用于评估）")
    parser.add_argument("--evaluate", action="store_true", help="评估模式")
    parser.add_argument("--confidence", "-c", type=float, default=0.9, help="人脸检测置信度阈值")
    parser.add_argument("--pair", nargs=2, metavar=('BEFORE', 'AFTER'), help="配对图片预测：before.jpg after.jpg")
    parser.add_argument("--pairing", action="store_true", help="使用配对模式")
    parser.add_argument("--face-crop", action="store_true", help="输入图片已经是裁剪好的人脸")

    args = parser.parse_args()

    if args.evaluate and args.dataset:
        # 评估模式
        evaluator = BMIEvaluator(args.model, args.dataset, confidence_threshold=args.confidence, use_pairing=args.pairing)
        results = evaluator.evaluate_dataset()

        print("评估结果:")
        print(f"MAE: {results['mae']:.4f}")
        print(f"RMSE: {results['rmse']:.4f}")
        print(f"样本数: {results['num_samples']}")

    elif args.pair:
        # 配对预测
        predictor = PairedBMIInference(args.model, confidence_threshold=args.confidence, use_pairing=True)

        if args.face_crop:
            # 直接使用预裁剪的人脸图片进行配对预测
            result = predictor.predict_from_face_crop_pair(args.pair[0], args.pair[1])
        else:
            # 需要先检测人脸
            result = predictor.predict_from_pair(args.pair[0], args.pair[1])

        if result is not None:
            print(f"起始BMI: {result['start_bmi']:.2f}")
            print(f"结束BMI: {result['end_bmi']:.2f}")
            print(f"BMI变化: {result['bmi_change']:.2f}")
        else:
            print("配对预测失败")

    elif args.image:
        # 单张图片预测
        predictor = PairedBMIInference(args.model, confidence_threshold=args.confidence, use_pairing=args.pairing)

        if args.face_crop:
            # 直接使用预裁剪的人脸图片
            bmi = predictor.predict_from_face_crop(args.image)
        else:
            # 需要先检测人脸
            bmi = predictor.predict_from_image(args.image)

        if bmi is not None:
            category = predictor.get_bmi_category(bmi)
            print(f"预测BMI: {bmi:.2f}")
            print(f"BMI分类: {category}")
        else:
            print("预测失败")

    else:
        print("请指定 --image 进行单张预测、--pair 进行配对预测或 --evaluate --dataset 进行评估")

if __name__ == "__main__":
    main()
