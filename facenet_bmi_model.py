"""
基于FaceNet的配对BMI预测模型
考虑同一个人前后两张图片的关系
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
from facenet_pytorch import InceptionResnetV1
import cv2
import numpy as np
import json
import os
from pathlib import Path
import logging
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error
import matplotlib.pyplot as plt

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PairedFaceDataset(Dataset):
    """配对人脸数据集"""
    
    def __init__(self, data_list, root_dir, transform=None, mode='paired'):
        self.data_list = data_list
        self.root_dir = Path(root_dir)
        self.transform = transform
        self.mode = mode
        
        if mode == 'single':
            self.expanded_data = []
            for item in data_list:
                self.expanded_data.append({
                    'image_path': item['before_image_path'],
                    'bmi': item['start_bmi'],
                    'type': 'before'
                })
                self.expanded_data.append({
                    'image_path': item['after_image_path'],
                    'bmi': item['end_bmi'],
                    'type': 'after'
                })
    
    def __len__(self):
        if self.mode == 'paired':
            return len(self.data_list)
        else:
            return len(self.expanded_data)
    
    def _load_image(self, image_path):
        image = cv2.imread(str(self.root_dir / image_path))
        if image is None:
            image = np.zeros((160, 160, 3), dtype=np.uint8)
        else:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        if self.transform:
            image = self.transform(image)
        
        return image
    
    def __getitem__(self, idx):
        if self.mode == 'paired':
            item = self.data_list[idx]
            
            before_image = self._load_image(item['before_image_path'])
            after_image = self._load_image(item['after_image_path'])
            
            start_bmi = torch.tensor(item['start_bmi'], dtype=torch.float32)
            end_bmi = torch.tensor(item['end_bmi'], dtype=torch.float32)
            bmi_change = end_bmi - start_bmi
            
            return {
                'before_image': before_image,
                'after_image': after_image,
                'start_bmi': start_bmi,
                'end_bmi': end_bmi,
                'bmi_change': bmi_change
            }
        else:
            item = self.expanded_data[idx]
            image = self._load_image(item['image_path'])
            bmi = torch.tensor(item['bmi'], dtype=torch.float32)
            
            return image, bmi

class PairedBMIPredictor(nn.Module):
    """配对BMI预测器"""
    
    def __init__(self, pretrained=True, dropout_rate=0.5, use_pairing=True):
        super(PairedBMIPredictor, self).__init__()
        
        self.use_pairing = use_pairing
        
        # FaceNet特征提取器
        self.facenet = InceptionResnetV1(pretrained='vggface2' if pretrained else None)
        
        # 冻结大部分层
        for param in self.facenet.parameters():
            param.requires_grad = False
        
        # 解冻最后几层
        for param in self.facenet.last_linear.parameters():
            param.requires_grad = True
        for param in self.facenet.last_bn.parameters():
            param.requires_grad = True
        
        if use_pairing:
            # 配对模式
            self.feature_fusion = nn.Sequential(
                nn.Linear(1024, 512),
                nn.ReLU(),
                nn.Dropout(dropout_rate)
            )
            
            self.relation_layer = nn.Sequential(
                nn.Linear(512, 256),
                nn.ReLU(),
                nn.Dropout(dropout_rate),
                nn.Linear(256, 128),
                nn.ReLU()
            )
            
            # 多任务输出
            self.start_bmi_head = nn.Sequential(
                nn.Dropout(dropout_rate),
                nn.Linear(128, 64),
                nn.ReLU(),
                nn.Linear(64, 1)
            )
            
            self.end_bmi_head = nn.Sequential(
                nn.Dropout(dropout_rate),
                nn.Linear(128, 64),
                nn.ReLU(),
                nn.Linear(64, 1)
            )
            
            self.bmi_change_head = nn.Sequential(
                nn.Dropout(dropout_rate),
                nn.Linear(128, 64),
                nn.ReLU(),
                nn.Linear(64, 1)
            )
        else:
            # 单张模式
            self.bmi_head = nn.Sequential(
                nn.Dropout(dropout_rate),
                nn.Linear(512, 256),
                nn.ReLU(),
                nn.Dropout(dropout_rate),
                nn.Linear(256, 128),
                nn.ReLU(),
                nn.Dropout(dropout_rate),
                nn.Linear(128, 1)
            )
    
    def forward(self, x):
        if self.use_pairing:
            if isinstance(x, dict):
                before_img = x['before_image']
                after_img = x['after_image']
            else:
                before_img = x[:, 0]
                after_img = x[:, 1]
            
            before_features = self.facenet(before_img)
            after_features = self.facenet(after_img)
            
            combined_features = torch.cat([before_features, after_features], dim=1)
            fused_features = self.feature_fusion(combined_features)
            relation_features = self.relation_layer(fused_features)
            
            start_bmi = self.start_bmi_head(relation_features).squeeze()
            end_bmi = self.end_bmi_head(relation_features).squeeze()
            bmi_change = self.bmi_change_head(relation_features).squeeze()
            
            return {
                'start_bmi': start_bmi,
                'end_bmi': end_bmi,
                'bmi_change': bmi_change
            }
        else:
            features = self.facenet(x)
            bmi = self.bmi_head(features)
            return bmi.squeeze()

class PairedBMITrainer:
    """配对BMI训练器"""
    
    def __init__(self, dataset_path, model_save_dir="models", use_pairing=True):
        self.dataset_path = dataset_path
        self.model_save_dir = Path(model_save_dir)
        self.model_save_dir.mkdir(exist_ok=True)
        self.use_pairing = use_pairing
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"使用设备: {self.device}")
        logger.info(f"训练模式: {'配对模式' if use_pairing else '单张模式'}")
        
        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((160, 160)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
        ])
        
        self.model = None
        self.train_loader = None
        self.val_loader = None
        self.test_loader = None
    
    def load_dataset(self):
        logger.info("加载数据集")
        
        with open(self.dataset_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        logger.info(f"总样本数: {len(data)}")
        
        train_data, temp_data = train_test_split(data, test_size=0.3, random_state=42)
        val_data, test_data = train_test_split(temp_data, test_size=0.5, random_state=42)
        
        logger.info(f"训练集: {len(train_data)}, 验证集: {len(val_data)}, 测试集: {len(test_data)}")
        
        root_dir = Path(self.dataset_path).parent
        
        if self.use_pairing:
            train_dataset = PairedFaceDataset(train_data, root_dir, self.transform, 'paired')
            val_dataset = PairedFaceDataset(val_data, root_dir, self.transform, 'paired')
            test_dataset = PairedFaceDataset(test_data, root_dir, self.transform, 'paired')
            batch_size = 16
        else:
            train_dataset = PairedFaceDataset(train_data, root_dir, self.transform, 'single')
            val_dataset = PairedFaceDataset(val_data, root_dir, self.transform, 'single')
            test_dataset = PairedFaceDataset(test_data, root_dir, self.transform, 'single')
            batch_size = 32
        
        self.train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=4)
        self.val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=4)
        self.test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=4)
        
        logger.info("数据集加载完成")
    
    def create_model(self):
        logger.info("创建配对BMI预测模型")
        
        self.model = PairedBMIPredictor(pretrained=True, use_pairing=self.use_pairing)
        self.model.to(self.device)
        
        if self.use_pairing:
            # 配对模式使用多任务损失
            self.criterion = nn.MSELoss()
        else:
            self.criterion = nn.MSELoss()
        
        self.optimizer = optim.Adam(self.model.parameters(), lr=0.001, weight_decay=1e-4)
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.5, patience=5, verbose=True
        )
        
        logger.info("模型创建完成")

    def train_epoch(self):
        self.model.train()
        total_loss = 0
        num_batches = 0

        for batch_idx, batch_data in enumerate(self.train_loader):
            self.optimizer.zero_grad()

            if self.use_pairing:
                # 配对模式
                batch_data = {k: v.to(self.device) for k, v in batch_data.items()}
                predictions = self.model(batch_data)

                # 多任务损失
                start_loss = self.criterion(predictions['start_bmi'], batch_data['start_bmi'])
                end_loss = self.criterion(predictions['end_bmi'], batch_data['end_bmi'])
                change_loss = self.criterion(predictions['bmi_change'], batch_data['bmi_change'])

                # 加权损失
                loss = start_loss + end_loss + 0.5 * change_loss
            else:
                # 单张模式
                images, bmis = batch_data
                images, bmis = images.to(self.device), bmis.to(self.device)
                predictions = self.model(images)
                loss = self.criterion(predictions, bmis)

            loss.backward()
            self.optimizer.step()

            total_loss += loss.item()
            num_batches += 1

            if batch_idx % 50 == 0:
                logger.info(f"Batch {batch_idx}/{len(self.train_loader)}, Loss: {loss.item():.4f}")

        return total_loss / num_batches

    def validate(self):
        self.model.eval()
        total_loss = 0
        predictions_list = []
        targets_list = []

        with torch.no_grad():
            for batch_data in self.val_loader:
                if self.use_pairing:
                    batch_data = {k: v.to(self.device) for k, v in batch_data.items()}
                    predictions = self.model(batch_data)

                    start_loss = self.criterion(predictions['start_bmi'], batch_data['start_bmi'])
                    end_loss = self.criterion(predictions['end_bmi'], batch_data['end_bmi'])
                    change_loss = self.criterion(predictions['bmi_change'], batch_data['bmi_change'])

                    loss = start_loss + end_loss + 0.5 * change_loss

                    # 收集预测结果用于评估
                    predictions_list.extend(predictions['start_bmi'].cpu().numpy())
                    predictions_list.extend(predictions['end_bmi'].cpu().numpy())
                    targets_list.extend(batch_data['start_bmi'].cpu().numpy())
                    targets_list.extend(batch_data['end_bmi'].cpu().numpy())
                else:
                    images, bmis = batch_data
                    images, bmis = images.to(self.device), bmis.to(self.device)
                    predictions = self.model(images)
                    loss = self.criterion(predictions, bmis)

                    predictions_list.extend(predictions.cpu().numpy())
                    targets_list.extend(bmis.cpu().numpy())

                total_loss += loss.item()

        avg_loss = total_loss / len(self.val_loader)
        mae = mean_absolute_error(targets_list, predictions_list)
        rmse = np.sqrt(mean_squared_error(targets_list, predictions_list))

        return avg_loss, mae, rmse

    def train(self, epochs=50):
        logger.info(f"开始训练，共{epochs}个epoch")

        best_val_loss = float('inf')
        train_losses = []
        val_losses = []

        for epoch in range(epochs):
            logger.info(f"Epoch {epoch+1}/{epochs}")

            train_loss = self.train_epoch()
            val_loss, mae, rmse = self.validate()

            self.scheduler.step(val_loss)

            train_losses.append(train_loss)
            val_losses.append(val_loss)

            logger.info(f"Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}, MAE: {mae:.4f}, RMSE: {rmse:.4f}")

            if val_loss < best_val_loss:
                best_val_loss = val_loss
                torch.save(self.model.state_dict(), self.model_save_dir / "best_paired_bmi_model.pth")
                logger.info("保存最佳模型")

        torch.save(self.model.state_dict(), self.model_save_dir / "final_paired_bmi_model.pth")

        # 绘制训练曲线
        plt.figure(figsize=(10, 6))
        plt.plot(train_losses, label='Train Loss')
        plt.plot(val_losses, label='Validation Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Paired BMI Model Training Curves')
        plt.legend()
        plt.grid(True)
        plt.savefig(self.model_save_dir / "paired_training_curves.png")
        plt.close()

        logger.info("训练完成")

    def test(self):
        logger.info("测试模型")

        self.model.load_state_dict(torch.load(self.model_save_dir / "best_paired_bmi_model.pth"))
        self.model.eval()

        predictions_list = []
        targets_list = []

        with torch.no_grad():
            for batch_data in self.test_loader:
                if self.use_pairing:
                    batch_data = {k: v.to(self.device) for k, v in batch_data.items()}
                    predictions = self.model(batch_data)

                    predictions_list.extend(predictions['start_bmi'].cpu().numpy())
                    predictions_list.extend(predictions['end_bmi'].cpu().numpy())
                    targets_list.extend(batch_data['start_bmi'].cpu().numpy())
                    targets_list.extend(batch_data['end_bmi'].cpu().numpy())
                else:
                    images, bmis = batch_data
                    images, bmis = images.to(self.device), bmis.to(self.device)
                    predictions = self.model(images)

                    predictions_list.extend(predictions.cpu().numpy())
                    targets_list.extend(bmis.cpu().numpy())

        mae = mean_absolute_error(targets_list, predictions_list)
        rmse = np.sqrt(mean_squared_error(targets_list, predictions_list))

        logger.info(f"测试结果 - MAE: {mae:.4f}, RMSE: {rmse:.4f}")

        return mae, rmse

def main():
    import argparse

    parser = argparse.ArgumentParser(description="配对FaceNet BMI预测模型训练")
    parser.add_argument("--dataset", "-d", default="face_dataset/face_dataset.json", help="数据集路径")
    parser.add_argument("--epochs", "-e", type=int, default=30, help="训练轮数")
    parser.add_argument("--model_dir", "-m", default="models", help="模型保存目录")
    parser.add_argument("--pairing", action="store_true", default=True, help="使用配对模式")
    parser.add_argument("--single", action="store_true", help="使用单张模式")

    args = parser.parse_args()

    if not os.path.exists(args.dataset):
        logger.error(f"数据集文件不存在: {args.dataset}")
        return

    use_pairing = not args.single

    trainer = PairedBMITrainer(args.dataset, args.model_dir, use_pairing)
    trainer.load_dataset()
    trainer.create_model()
    trainer.train(args.epochs)
    mae, rmse = trainer.test()

    print(f"最终测试结果:")
    print(f"MAE: {mae:.4f}")
    print(f"RMSE: {rmse:.4f}")

if __name__ == "__main__":
    main()
