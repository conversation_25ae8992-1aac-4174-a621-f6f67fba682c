"""
基于FaceNet的配对BMI预测模型
考虑同一个人前后两张图片的关系
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
from facenet_pytorch import InceptionResnetV1
import cv2
import numpy as np
import json
import os
from pathlib import Path
import logging
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error
import matplotlib.pyplot as plt

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PairedFaceDataset(Dataset):
    """配对人脸数据集"""
    
    def __init__(self, data_list, root_dir, transform=None, mode='paired'):
        self.data_list = data_list
        self.root_dir = Path(root_dir)
        self.transform = transform
        self.mode = mode
        
        if mode == 'single':
            self.expanded_data = []
            for item in data_list:
                self.expanded_data.append({
                    'image_path': item['before_image_path'],
                    'bmi': item['start_bmi'],
                    'type': 'before'
                })
                self.expanded_data.append({
                    'image_path': item['after_image_path'],
                    'bmi': item['end_bmi'],
                    'type': 'after'
                })
    
    def __len__(self):
        if self.mode == 'paired':
            return len(self.data_list)
        else:
            return len(self.expanded_data)
    
    def _load_image(self, image_path):
        image = cv2.imread(str(self.root_dir / image_path))
        if image is None:
            image = np.zeros((160, 160, 3), dtype=np.uint8)
        else:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        if self.transform:
            image = self.transform(image)
        
        return image
    
    def __getitem__(self, idx):
        if self.mode == 'paired':
            item = self.data_list[idx]
            
            before_image = self._load_image(item['before_image_path'])
            after_image = self._load_image(item['after_image_path'])
            
            start_bmi = torch.tensor(item['start_bmi'], dtype=torch.float32)
            end_bmi = torch.tensor(item['end_bmi'], dtype=torch.float32)
            bmi_change = end_bmi - start_bmi
            
            return {
                'before_image': before_image,
                'after_image': after_image,
                'start_bmi': start_bmi,
                'end_bmi': end_bmi,
                'bmi_change': bmi_change
            }
        else:
            item = self.expanded_data[idx]
            image = self._load_image(item['image_path'])
            bmi = torch.tensor(item['bmi'], dtype=torch.float32)
            
            return image, bmi

class CrossAttention(nn.Module):
    """交叉注意力机制"""
    def __init__(self, feature_dim=512, num_heads=8):
        super(CrossAttention, self).__init__()
        self.num_heads = num_heads
        self.feature_dim = feature_dim
        self.head_dim = feature_dim // num_heads

        self.query = nn.Linear(feature_dim, feature_dim)
        self.key = nn.Linear(feature_dim, feature_dim)
        self.value = nn.Linear(feature_dim, feature_dim)
        self.out = nn.Linear(feature_dim, feature_dim)
        self.dropout = nn.Dropout(0.1)

    def forward(self, before_feat, after_feat):
        batch_size = before_feat.size(0)

        # 计算注意力权重
        q = self.query(before_feat).view(batch_size, self.num_heads, self.head_dim)
        k = self.key(after_feat).view(batch_size, self.num_heads, self.head_dim)
        v = self.value(after_feat).view(batch_size, self.num_heads, self.head_dim)

        # 注意力计算
        attention = torch.matmul(q, k.transpose(-2, -1)) / (self.head_dim ** 0.5)
        attention = torch.softmax(attention, dim=-1)
        attention = self.dropout(attention)

        # 应用注意力
        out = torch.matmul(attention, v)
        out = out.view(batch_size, self.feature_dim)
        out = self.out(out)

        return out

class BilinearFusion(nn.Module):
    """双线性特征融合"""
    def __init__(self, input_dim=512, output_dim=512):
        super(BilinearFusion, self).__init__()
        self.bilinear = nn.Bilinear(input_dim, input_dim, output_dim)
        self.norm = nn.LayerNorm(output_dim)
        self.dropout = nn.Dropout(0.1)

    def forward(self, before_feat, after_feat):
        fused = self.bilinear(before_feat, after_feat)
        fused = self.norm(fused)
        fused = self.dropout(fused)
        return fused

class EnhancedPairedBMIPredictor(nn.Module):
    """增强版配对BMI预测器"""

    def __init__(self, pretrained=True, dropout_rate=0.3, use_pairing=True):
        super(EnhancedPairedBMIPredictor, self).__init__()

        self.use_pairing = use_pairing

        # FaceNet特征提取器
        self.facenet = InceptionResnetV1(pretrained='vggface2' if pretrained else None)

        # 渐进式解冻策略
        for param in self.facenet.parameters():
            param.requires_grad = False

        # 解冻更多层以提高表达能力
        for param in self.facenet.last_linear.parameters():
            param.requires_grad = True
        for param in self.facenet.last_bn.parameters():
            param.requires_grad = True
        # 解冻倒数第二个block
        if hasattr(self.facenet, 'repeat_3'):
            for param in self.facenet.repeat_3[-1].parameters():
                param.requires_grad = True

        if use_pairing:
            # 增强的配对模式
            # 1. 交叉注意力机制
            self.cross_attention = CrossAttention(feature_dim=512, num_heads=8)

            # 2. 双线性特征融合
            self.bilinear_fusion = BilinearFusion(input_dim=512, output_dim=512)

            # 3. 增强的特征融合网络
            self.feature_fusion = nn.Sequential(
                nn.Linear(1024, 768),
                nn.LayerNorm(768),
                nn.ReLU(),
                nn.Dropout(dropout_rate),
                nn.Linear(768, 512),
                nn.LayerNorm(512),
                nn.ReLU(),
                nn.Dropout(dropout_rate)
            )

            # 4. 深层关系建模网络
            self.relation_layer = nn.Sequential(
                nn.Linear(512, 384),
                nn.LayerNorm(384),
                nn.ReLU(),
                nn.Dropout(dropout_rate),
                nn.Linear(384, 256),
                nn.LayerNorm(256),
                nn.ReLU(),
                nn.Dropout(dropout_rate),
                nn.Linear(256, 128),
                nn.LayerNorm(128),
                nn.ReLU()
            )

            # 5. 增强的多任务输出头
            self.start_bmi_head = nn.Sequential(
                nn.Dropout(dropout_rate),
                nn.Linear(128, 96),
                nn.LayerNorm(96),
                nn.ReLU(),
                nn.Linear(96, 64),
                nn.ReLU(),
                nn.Linear(64, 1)
            )

            self.end_bmi_head = nn.Sequential(
                nn.Dropout(dropout_rate),
                nn.Linear(128, 96),
                nn.LayerNorm(96),
                nn.ReLU(),
                nn.Linear(96, 64),
                nn.ReLU(),
                nn.Linear(64, 1)
            )

            self.bmi_change_head = nn.Sequential(
                nn.Dropout(dropout_rate),
                nn.Linear(128, 96),
                nn.LayerNorm(96),
                nn.ReLU(),
                nn.Linear(96, 64),
                nn.ReLU(),
                nn.Linear(64, 1)
            )

            # 6. 任务权重学习网络
            self.task_weights = nn.Parameter(torch.ones(3))

        else:
            # 增强的单张模式
            self.bmi_head = nn.Sequential(
                nn.Dropout(dropout_rate),
                nn.Linear(512, 384),
                nn.LayerNorm(384),
                nn.ReLU(),
                nn.Dropout(dropout_rate),
                nn.Linear(384, 256),
                nn.LayerNorm(256),
                nn.ReLU(),
                nn.Dropout(dropout_rate),
                nn.Linear(256, 128),
                nn.ReLU(),
                nn.Linear(128, 1)
            )
    
    def forward(self, x):
        if self.use_pairing:
            if isinstance(x, dict):
                before_img = x['before_image']
                after_img = x['after_image']
            else:
                before_img = x[:, 0]
                after_img = x[:, 1]

            # 特征提取
            before_features = self.facenet(before_img)
            after_features = self.facenet(after_img)

            # 交叉注意力增强
            attended_before = self.cross_attention(before_features, after_features)
            attended_after = self.cross_attention(after_features, before_features)

            # 双线性融合
            bilinear_fused = self.bilinear_fusion(attended_before, attended_after)

            # 传统拼接融合
            combined_features = torch.cat([before_features, after_features], dim=1)
            linear_fused = self.feature_fusion(combined_features)

            # 融合两种特征
            final_features = linear_fused + bilinear_fused

            # 关系建模
            relation_features = self.relation_layer(final_features)

            # 多任务预测
            start_bmi = self.start_bmi_head(relation_features).squeeze()
            end_bmi = self.end_bmi_head(relation_features).squeeze()
            bmi_change = self.bmi_change_head(relation_features).squeeze()

            return {
                'start_bmi': start_bmi,
                'end_bmi': end_bmi,
                'bmi_change': bmi_change,
                'task_weights': torch.softmax(self.task_weights, dim=0)
            }
        else:
            features = self.facenet(x)
            bmi = self.bmi_head(features)
            return bmi.squeeze()

class AdaptiveLoss(nn.Module):
    """自适应多任务损失函数"""
    def __init__(self, num_tasks=3):
        super(AdaptiveLoss, self).__init__()
        self.num_tasks = num_tasks
        self.log_vars = nn.Parameter(torch.zeros(num_tasks))

    def forward(self, losses):
        """
        losses: [start_loss, end_loss, change_loss]
        """
        precision = torch.exp(-self.log_vars)
        loss = torch.sum(precision * losses + self.log_vars)
        return loss

class ConsistencyLoss(nn.Module):
    """一致性损失函数"""
    def __init__(self, weight=1.0):
        super(ConsistencyLoss, self).__init__()
        self.weight = weight

    def forward(self, start_bmi, end_bmi, bmi_change):
        predicted_change = end_bmi - start_bmi
        consistency_loss = torch.mean((bmi_change - predicted_change) ** 2)
        return self.weight * consistency_loss

class EnhancedPairedBMITrainer:
    """增强版配对BMI训练器"""

    def __init__(self, dataset_path, model_save_dir="models", use_pairing=True):
        self.dataset_path = dataset_path
        self.model_save_dir = Path(model_save_dir)
        self.model_save_dir.mkdir(exist_ok=True)
        self.use_pairing = use_pairing

        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"使用设备: {self.device}")
        logger.info(f"训练模式: {'增强配对模式' if use_pairing else '增强单张模式'}")

        # 增强的数据变换
        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((160, 160)),
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
            transforms.RandomRotation(degrees=10),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
        ])

        self.model = None
        self.train_loader = None
        self.val_loader = None
        self.test_loader = None

        # 增强的损失函数
        self.adaptive_loss = AdaptiveLoss(num_tasks=3)
        self.consistency_loss = ConsistencyLoss(weight=0.5)
        self.base_criterion = nn.MSELoss()
    
    def load_dataset(self):
        logger.info("加载数据集")
        
        with open(self.dataset_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        logger.info(f"总样本数: {len(data)}")
        
        train_data, temp_data = train_test_split(data, test_size=0.3, random_state=42)
        val_data, test_data = train_test_split(temp_data, test_size=0.5, random_state=42)
        
        logger.info(f"训练集: {len(train_data)}, 验证集: {len(val_data)}, 测试集: {len(test_data)}")
        
        root_dir = Path(self.dataset_path).parent
        
        if self.use_pairing:
            train_dataset = PairedFaceDataset(train_data, root_dir, self.transform, 'paired')
            val_dataset = PairedFaceDataset(val_data, root_dir, self.transform, 'paired')
            test_dataset = PairedFaceDataset(test_data, root_dir, self.transform, 'paired')
            batch_size = 16
        else:
            train_dataset = PairedFaceDataset(train_data, root_dir, self.transform, 'single')
            val_dataset = PairedFaceDataset(val_data, root_dir, self.transform, 'single')
            test_dataset = PairedFaceDataset(test_data, root_dir, self.transform, 'single')
            batch_size = 32
        
        self.train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=4)
        self.val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=4)
        self.test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=4)
        
        logger.info("数据集加载完成")
    
    def create_model(self):
        logger.info("创建增强版配对BMI预测模型")

        self.model = EnhancedPairedBMIPredictor(pretrained=True, use_pairing=self.use_pairing)
        self.model.to(self.device)

        # 将损失函数移到设备上
        self.adaptive_loss.to(self.device)
        self.consistency_loss.to(self.device)

        # 使用更先进的优化器
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=0.0005,  # 降低学习率
            weight_decay=1e-3,  # 增加权重衰减
            betas=(0.9, 0.999)
        )

        # 使用余弦退火学习率调度
        self.scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer, T_0=10, T_mult=2, eta_min=1e-6
        )

        logger.info("增强版模型创建完成")

    def train_epoch(self):
        self.model.train()
        total_loss = 0
        num_batches = 0

        for batch_idx, batch_data in enumerate(self.train_loader):
            self.optimizer.zero_grad()

            if self.use_pairing:
                # 增强配对模式
                batch_data = {k: v.to(self.device) for k, v in batch_data.items()}
                predictions = self.model(batch_data)

                # 基础多任务损失
                start_loss = self.base_criterion(predictions['start_bmi'], batch_data['start_bmi'])
                end_loss = self.base_criterion(predictions['end_bmi'], batch_data['end_bmi'])
                change_loss = self.base_criterion(predictions['bmi_change'], batch_data['bmi_change'])

                # 自适应权重损失
                losses = torch.stack([start_loss, end_loss, change_loss])
                adaptive_loss = self.adaptive_loss(losses)

                # 一致性损失
                consistency_loss = self.consistency_loss(
                    predictions['start_bmi'],
                    predictions['end_bmi'],
                    predictions['bmi_change']
                )

                # 总损失
                loss = adaptive_loss + consistency_loss

                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

            else:
                # 增强单张模式
                images, bmis = batch_data
                images, bmis = images.to(self.device), bmis.to(self.device)
                predictions = self.model(images)
                loss = self.base_criterion(predictions, bmis)

            loss.backward()
            self.optimizer.step()

            total_loss += loss.item()
            num_batches += 1

            if batch_idx % 50 == 0:
                if self.use_pairing:
                    task_weights = predictions['task_weights'].detach().cpu().numpy()
                    logger.info(f"Batch {batch_idx}/{len(self.train_loader)}, "
                              f"Loss: {loss.item():.4f}, "
                              f"Weights: [{task_weights[0]:.3f}, {task_weights[1]:.3f}, {task_weights[2]:.3f}]")
                else:
                    logger.info(f"Batch {batch_idx}/{len(self.train_loader)}, Loss: {loss.item():.4f}")

        return total_loss / num_batches

    def validate(self):
        self.model.eval()
        total_loss = 0
        predictions_list = []
        targets_list = []

        with torch.no_grad():
            for batch_data in self.val_loader:
                if self.use_pairing:
                    batch_data = {k: v.to(self.device) for k, v in batch_data.items()}
                    predictions = self.model(batch_data)

                    start_loss = self.criterion(predictions['start_bmi'], batch_data['start_bmi'])
                    end_loss = self.criterion(predictions['end_bmi'], batch_data['end_bmi'])
                    change_loss = self.criterion(predictions['bmi_change'], batch_data['bmi_change'])

                    loss = start_loss + end_loss + 0.5 * change_loss

                    # 收集预测结果用于评估
                    predictions_list.extend(predictions['start_bmi'].cpu().numpy())
                    predictions_list.extend(predictions['end_bmi'].cpu().numpy())
                    targets_list.extend(batch_data['start_bmi'].cpu().numpy())
                    targets_list.extend(batch_data['end_bmi'].cpu().numpy())
                else:
                    images, bmis = batch_data
                    images, bmis = images.to(self.device), bmis.to(self.device)
                    predictions = self.model(images)
                    loss = self.criterion(predictions, bmis)

                    predictions_list.extend(predictions.cpu().numpy())
                    targets_list.extend(bmis.cpu().numpy())

                total_loss += loss.item()

        avg_loss = total_loss / len(self.val_loader)
        mae = mean_absolute_error(targets_list, predictions_list)
        rmse = np.sqrt(mean_squared_error(targets_list, predictions_list))

        return avg_loss, mae, rmse

    def train(self, epochs=50):
        logger.info(f"开始训练，共{epochs}个epoch")

        best_val_loss = float('inf')
        train_losses = []
        val_losses = []

        for epoch in range(epochs):
            logger.info(f"Epoch {epoch+1}/{epochs}")

            train_loss = self.train_epoch()
            val_loss, mae, rmse = self.validate()

            self.scheduler.step(val_loss)

            train_losses.append(train_loss)
            val_losses.append(val_loss)

            logger.info(f"Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}, MAE: {mae:.4f}, RMSE: {rmse:.4f}")

            if val_loss < best_val_loss:
                best_val_loss = val_loss
                torch.save(self.model.state_dict(), self.model_save_dir / "best_paired_bmi_model.pth")
                logger.info("保存最佳模型")

        torch.save(self.model.state_dict(), self.model_save_dir / "final_paired_bmi_model.pth")

        # 绘制训练曲线
        plt.figure(figsize=(10, 6))
        plt.plot(train_losses, label='Train Loss')
        plt.plot(val_losses, label='Validation Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Paired BMI Model Training Curves')
        plt.legend()
        plt.grid(True)
        plt.savefig(self.model_save_dir / "paired_training_curves.png")
        plt.close()

        logger.info("训练完成")

    def test(self):
        logger.info("测试模型")

        self.model.load_state_dict(torch.load(self.model_save_dir / "best_paired_bmi_model.pth"))
        self.model.eval()

        predictions_list = []
        targets_list = []

        with torch.no_grad():
            for batch_data in self.test_loader:
                if self.use_pairing:
                    batch_data = {k: v.to(self.device) for k, v in batch_data.items()}
                    predictions = self.model(batch_data)

                    predictions_list.extend(predictions['start_bmi'].cpu().numpy())
                    predictions_list.extend(predictions['end_bmi'].cpu().numpy())
                    targets_list.extend(batch_data['start_bmi'].cpu().numpy())
                    targets_list.extend(batch_data['end_bmi'].cpu().numpy())
                else:
                    images, bmis = batch_data
                    images, bmis = images.to(self.device), bmis.to(self.device)
                    predictions = self.model(images)

                    predictions_list.extend(predictions.cpu().numpy())
                    targets_list.extend(bmis.cpu().numpy())

        mae = mean_absolute_error(targets_list, predictions_list)
        rmse = np.sqrt(mean_squared_error(targets_list, predictions_list))

        logger.info(f"测试结果 - MAE: {mae:.4f}, RMSE: {rmse:.4f}")

        return mae, rmse

def main():
    import argparse

    parser = argparse.ArgumentParser(description="配对FaceNet BMI预测模型训练")
    parser.add_argument("--dataset", "-d", default="face_dataset/face_dataset.json", help="数据集路径")
    parser.add_argument("--epochs", "-e", type=int, default=30, help="训练轮数")
    parser.add_argument("--model_dir", "-m", default="models", help="模型保存目录")
    parser.add_argument("--pairing", action="store_true", default=True, help="使用配对模式")
    parser.add_argument("--single", action="store_true", help="使用单张模式")

    args = parser.parse_args()

    if not os.path.exists(args.dataset):
        logger.error(f"数据集文件不存在: {args.dataset}")
        return

    use_pairing = not args.single

    trainer = PairedBMITrainer(args.dataset, args.model_dir, use_pairing)
    trainer.load_dataset()
    trainer.create_model()
    trainer.train(args.epochs)
    mae, rmse = trainer.test()

    print(f"最终测试结果:")
    print(f"MAE: {mae:.4f}")
    print(f"RMSE: {rmse:.4f}")

if __name__ == "__main__":
    main()
