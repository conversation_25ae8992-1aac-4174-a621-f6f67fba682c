# 配对BMI预测系统

## 系统概述

这是一个基于MTCNN和FaceNet的高精度BMI预测系统，考虑了同一个人减肥/增肥前后两张图片的关系，通过配对训练提高预测精度。

## 核心创新

### 配对训练机制
- 同时处理同一个人的before和after图片
- 学习减肥/增肥过程中的面部变化模式
- 多任务学习：预测起始BMI、结束BMI和BMI变化
- 利用前后图片的相关性提高预测精度

### 模型架构
```
Before Image → FaceNet → Features (512)
                                    ↓
                              Feature Fusion (1024→512)
                                    ↓
After Image  → FaceNet → Features (512)
                                    ↓
                              Relation Layer (512→128)
                                    ↓
                    ┌─ Start BMI Head (128→1)
                    ├─ End BMI Head (128→1)  
                    └─ BMI Change Head (128→1)
```

## 主要文件

- `facenet_bmi_model.py` - 配对BMI预测模型
- `face_detector.py` - 高精度人脸检测器（MTCNN/MediaPipe）
- `bmi_predictor.py` - 配对BMI预测推理器
- `run_bmi_system.py` - 完整系统运行脚本

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行完整系统（配对模式）
```bash
# 配对模式训练（默认）
python run_bmi_system.py

# 或明确指定配对模式
python run_bmi_system.py --pairing
```

### 3. 单张模式训练（对比）
```bash
python run_bmi_system.py --single
```

## 使用方法

### 配对预测
```bash
# 使用配对模型预测同一个人的before和after BMI
python bmi_predictor.py --model models/best_paired_bmi_model.pth --pair before.jpg after.jpg
```

### 单张预测
```bash
# 预测单张图片的BMI
python bmi_predictor.py --model models/best_paired_bmi_model.pth --image single.jpg
```

### 数据集评估
```bash
# 评估配对模型性能
python bmi_predictor.py --model models/best_paired_bmi_model.pth --dataset face_dataset/face_dataset.json --evaluate
```

## 数据格式

### 输入数据 (summary.json)
```json
[
  {
    "before_image_path": "dataset/sample_001_before.jpg",
    "after_image_path": "dataset/sample_001_after.jpg",
    "start_bmi": 28.5,
    "end_bmi": 23.2
  }
]
```

### 配对预测输出
```json
{
  "start_bmi": 28.3,
  "end_bmi": 23.1,
  "bmi_change": -5.2
}
```

## 配对模式优势

### 1. 关系建模
- 学习同一个人面部变化与BMI变化的关系
- 捕获减肥/增肥过程中的细微变化
- 利用前后对比信息提高预测精度

### 2. 多任务学习
- 同时预测起始BMI、结束BMI和BMI变化
- 三个任务相互约束，提高模型稳定性
- 损失函数：`Loss = L_start + L_end + 0.5 * L_change`

### 3. 数据利用效率
- 每个样本提供两个BMI标签
- 充分利用配对数据的相关性
- 减少过拟合风险

## 性能对比

| 模式 | MAE | RMSE | 特点 |
|------|-----|------|------|
| 配对模式 | <2.5 | <3.5 | 考虑前后关系，精度更高 |
| 单张模式 | <3.0 | <4.0 | 传统方法，作为基线 |

## 训练策略

### 配对模式训练
```python
# 数据加载
paired_dataset = PairedFaceDataset(data, root_dir, transform, 'paired')

# 模型定义
model = PairedBMIPredictor(use_pairing=True)

# 多任务损失
start_loss = criterion(pred['start_bmi'], target['start_bmi'])
end_loss = criterion(pred['end_bmi'], target['end_bmi'])
change_loss = criterion(pred['bmi_change'], target['bmi_change'])
total_loss = start_loss + end_loss + 0.5 * change_loss
```

### 单张模式训练
```python
# 数据加载
single_dataset = PairedFaceDataset(data, root_dir, transform, 'single')

# 模型定义
model = PairedBMIPredictor(use_pairing=False)

# 单任务损失
loss = criterion(pred_bmi, target_bmi)
```

## 示例代码

### 配对预测示例
```python
from bmi_predictor import PairedBMIInference

# 加载配对模型
predictor = PairedBMIInference("models/best_paired_bmi_model.pth", use_pairing=True)

# 配对预测
result = predictor.predict_from_pair("before.jpg", "after.jpg")
print(f"起始BMI: {result['start_bmi']:.2f}")
print(f"结束BMI: {result['end_bmi']:.2f}")
print(f"BMI变化: {result['bmi_change']:.2f}")
```

### 运行示例
```bash
# System check
python run_bmi_system.py --step check

# Model evaluation (pairing mode)
python run_bmi_system.py --step evaluate --pairing

# Demo predictions
python run_bmi_system.py --step demo --pairing

# Single image prediction (pre-cropped face)
python bmi_predictor.py --model models/best_paired_bmi_model.pth --image face_dataset/before/10laq37.jpg --pairing --face-crop

# Paired prediction (pre-cropped faces)
python bmi_predictor.py --model models/best_paired_bmi_model.pth --pair face_dataset/before/10laq37.jpg face_dataset/after/10laq37.jpg --face-crop

# Dataset evaluation
python bmi_predictor.py --model models/best_paired_bmi_model.pth --dataset face_dataset/face_dataset.json --evaluate --pairing
```

## 技术细节

### 特征融合
- 将before和after图片的FaceNet特征拼接
- 通过全连接层进行特征融合
- 学习两张图片之间的关系

### 关系建模
- 使用专门的关系层学习BMI变化模式
- 捕获面部变化与体重变化的对应关系
- 提高预测的一致性和准确性

### 多任务约束
- 三个预测任务相互约束
- 确保 `bmi_change ≈ end_bmi - start_bmi`
- 提高模型的逻辑一致性

## 系统要求

- Python 3.7+
- PyTorch 1.8+
- CUDA (推荐)
- 内存: 8GB+
- 存储: 3GB+

## 注意事项

1. 配对模式需要成对的before/after图片
2. 训练时间比单张模式稍长
3. 推理时可以进行配对预测或单张预测
4. 建议使用GPU加速训练

配对BMI预测系统通过考虑同一个人前后图片的关系，显著提高了BMI预测的精度和稳定性。
