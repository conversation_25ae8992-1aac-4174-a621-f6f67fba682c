"""
Reddit Progress Pics爬虫
"""

import json
import re
import os
import time
import html
import requests
from PIL import Image

def extract_stats(title):
    """提取身高体重信息"""
    title = html.unescape(title)

    # 身高格式：覆盖Reddit上所有可能的写法
    height_patterns = [
        # 英制格式
        r"(\d+\'(?:\d+\")?)",           # 5'8" 或 5'8
        r"(\d+ft\s*\d*(?:in)?)",       # 5ft8, 5ft8in, 5ft
        r"(\d+\s*feet?\s*\d*)",        # 5 feet 8 或 5 foot
        r"(\d+\'\d+)",                 # 5'8 (无引号)
        r"(\d+\.\d+)\'",               # 5.8' 格式

        # 公制格式
        r"(\d{3})\s*cm",               # 168cm
        r"(\d\.\d{1,2})\s*m",          # 1.68m, 1.7m
        r"(\d)m(\d{2})",               # 1m62, 1m68
        r"(\d+)\s*cm",                 # 162cm (更宽泛)
        r"(\d+\.\d+)\s*m",             # 1.68m (更宽泛)

        # 特殊格式和变体
        r"/(\d+\'(?:\d+\")?)/",        # /5'8"/
        r"/(\d{3}cm)/",                # /168cm/
        r"/(\d\.\d{1,2}m)/",           # /1.68m/
        r"(\d+)cm",                    # 168cm (无空格)
        r"(\d+\'\d+\")",               # 5'8" (完整格式)
    ]

    # 体重格式：覆盖Reddit上所有可能的写法
    weight_patterns = [
        # 标准方括号格式 [起始重量 > 结束重量 = 差值]
        r"\[(\d+(?:\.\d+)?)\s*(?:lbs?|kg|pounds?|kilos?)?\s*[>→\-–—<]\s*(\d+(?:\.\d+)?)\s*(?:lbs?|kg|pounds?|kilos?)?\s*=\s*\d+",
        r"\[(\d+(?:\.\d+)?)\s*(?:lbs?|kg|pounds?|kilos?)?\s*[>→\-–—<]\s*(\d+(?:\.\d+)?)\s*(?:lbs?|kg|pounds?|kilos?)?\]",
        r"\[(\d+(?:\.\d+)?)\s*[>→\-–—<]\s*(\d+(?:\.\d+)?)\s*=\s*\d+",
        r"\[(\d+(?:\.\d+)?)\s*[>→\-–—<]\s*(\d+(?:\.\d+)?)\]",

        # 标准格式 with 各种分隔符
        r"(\d+(?:\.\d+)?)\s*(?:lbs?|kg|pounds?|kilos?)?\s*[>→\-–—<]\s*(\d+(?:\.\d+)?)\s*(?:lbs?|kg|pounds?|kilos?)?",
        r"(\d+(?:\.\d+)?)\s*(?:lbs?|kg|pounds?|kilos?)?\s*to\s*(\d+(?:\.\d+)?)\s*(?:lbs?|kg|pounds?|kilos?)?",
        r"(\d+(?:\.\d+)?)\s*(?:lbs?|kg|pounds?|kilos?)?\s*→\s*(\d+(?:\.\d+)?)\s*(?:lbs?|kg|pounds?|kilos?)?",
        r"(\d+(?:\.\d+)?)\s*(?:lbs?|kg|pounds?|kilos?)?\s*>\s*(\d+(?:\.\d+)?)\s*(?:lbs?|kg|pounds?|kilos?)?",
        r"(\d+(?:\.\d+)?)\s*(?:lbs?|kg|pounds?|kilos?)?\s*<\s*(\d+(?:\.\d+)?)\s*(?:lbs?|kg|pounds?|kilos?)?",
        r"(\d+(?:\.\d+)?)\s*(?:lbs?|kg|pounds?|kilos?)?\s*-\s*(\d+(?:\.\d+)?)\s*(?:lbs?|kg|pounds?|kilos?)?",

        # HTML实体和特殊字符
        r"(\d+(?:\.\d+)?)\s*(?:lbs?|kg|pounds?|kilos?)?\s*&gt;\s*(\d+(?:\.\d+)?)\s*(?:lbs?|kg|pounds?|kilos?)?",
        r"(\d+(?:\.\d+)?)\s*(?:lbs?|kg|pounds?|kilos?)?\s*&lt;\s*(\d+(?:\.\d+)?)\s*(?:lbs?|kg|pounds?|kilos?)?",

        # 无单位格式
        r"(\d+(?:\.\d+)?)\s*[>→\-–—<]\s*(\d+(?:\.\d+)?)\s*=",
        r"\[(\d+(?:\.\d+)?)\s*[>→\-–—<]\s*(\d+(?:\.\d+)?)\s*=",
        r"(\d+(?:\.\d+)?)\s*[>→\-–—<]\s*(\d+(?:\.\d+)?)",

        # 特殊Reddit格式
        r"SW:\s*(\d+(?:\.\d+)?)\s*(?:lbs?|kg)?\s*[,\s]+CW:\s*(\d+(?:\.\d+)?)\s*(?:lbs?|kg)?",  # SW: 180 CW: 140
        r"(\d+(?:\.\d+)?)\s*(?:lbs?|kg)?\s*→\s*(\d+(?:\.\d+)?)\s*(?:lbs?|kg)?",
        r"(\d+(?:\.\d+)?)\s*(?:lbs?|kg)?\s*->\s*(\d+(?:\.\d+)?)\s*(?:lbs?|kg)?",

        # 更宽松的匹配
        r"(\d{2,3}(?:\.\d+)?)\s*(?:lbs?|kg|pounds?|kilos?)?\s*[>→\-–—<~]\s*(\d{2,3}(?:\.\d+)?)\s*(?:lbs?|kg|pounds?|kilos?)?",
    ]

    height = None
    start_weight = None
    end_weight = None
    height_unit = None
    weight_unit = None

    # 尝试匹配身高
    for pattern in height_patterns:
        height_match = re.search(pattern, title, re.IGNORECASE)
        if height_match:
            if "m" in pattern and "cm" not in pattern:
                if len(height_match.groups()) == 2:  # 1m62格式
                    height = f"{height_match.group(1)}.{height_match.group(2)}"
                    height_unit = "m"
                else:
                    height = height_match.group(1)
                    height_unit = "m"
            elif "cm" in pattern:
                height = height_match.group(1)
                height_unit = "cm"
            else:
                height = height_match.group(1)
                height_unit = "ft"
            break

    # 尝试匹配体重并检测单位
    for pattern in weight_patterns:
        weight_match = re.search(pattern, title, re.IGNORECASE)
        if weight_match:
            start_weight = weight_match.group(1)
            end_weight = weight_match.group(2)

            # 检测体重单位
            full_match = weight_match.group(0)
            if re.search(r'kg|kilos?', full_match, re.IGNORECASE):
                weight_unit = "kg"
            elif re.search(r'lbs?|pounds?', full_match, re.IGNORECASE):
                weight_unit = "lbs"
            else:
                # 根据数值范围推断单位
                try:
                    start_w = float(start_weight)
                    end_w = float(end_weight)
                    # 如果数值在40-200范围，更可能是kg；如果在80-500范围，更可能是lbs
                    if 40 <= start_w <= 200 and 40 <= end_w <= 200:
                        weight_unit = "kg"
                    elif 80 <= start_w <= 500 and 80 <= end_w <= 500:
                        weight_unit = "lbs"
                    else:
                        weight_unit = "unknown"
                except ValueError:
                    weight_unit = "unknown"
            break

    # 验证提取的数据
    if height and start_weight and end_weight:
        try:
            start_w = float(start_weight)
            end_w = float(end_weight)
            # 更宽松的合理性检查
            if 20 <= start_w <= 1000 and 20 <= end_w <= 1000:
                return {
                    "height": height,
                    "height_unit": height_unit,
                    "start_weight": start_weight,
                    "end_weight": end_weight,
                    "weight_unit": weight_unit
                }
        except ValueError:
            pass

    return None

def convert_height_to_meters(height_str, height_unit):
    """将身高转换为米"""
    try:
        if height_unit == "ft":
            # 英制：5'8" 或 5'8 或 5ft8
            if "'" in height_str:
                match = re.match(r"(\d+)'(?:(\d+)\"?)?", height_str)
                if match:
                    feet = int(match.group(1))
                    inches = int(match.group(2)) if match.group(2) else 0
                    total_inches = feet * 12 + inches
                    meters = total_inches * 0.0254
                    return round(meters, 2)
            elif "ft" in height_str:
                match = re.match(r"(\d+)ft\s*(\d*)", height_str)
                if match:
                    feet = int(match.group(1))
                    inches = int(match.group(2)) if match.group(2) else 0
                    total_inches = feet * 12 + inches
                    meters = total_inches * 0.0254
                    return round(meters, 2)

        elif height_unit == "cm":
            # 厘米转米
            cm = float(height_str)
            return round(cm / 100, 2)

        elif height_unit == "m":
            # 已经是米
            return round(float(height_str), 2)

        else:
            # 尝试自动识别
            height_val = float(height_str)
            if height_val > 10:  # 可能是厘米
                return round(height_val / 100, 2)
            else:  # 可能是米
                return round(height_val, 2)

    except (ValueError, AttributeError):
        pass

    return None

def convert_weight_to_kg(weight_str, weight_unit):
    """将体重转换为公斤"""
    try:
        weight = float(weight_str)

        if weight_unit == "kg":
            # 已经是公斤
            return round(weight, 1)
        elif weight_unit == "lbs":
            # 磅转公斤
            return round(weight * 0.453592, 1)
        else:
            # 根据数值范围自动判断
            if 20 <= weight <= 200:
                # 更可能是公斤
                return round(weight, 1)
            elif 40 <= weight <= 500:
                # 更可能是磅
                return round(weight * 0.453592, 1)
            else:
                # 默认按磅处理
                return round(weight * 0.453592, 1)

    except ValueError:
        pass

    return None

def calculate_bmi(weight_kg, height_m):
    """计算BMI"""
    if height_m > 0:
        bmi = weight_kg / (height_m ** 2)
        return round(bmi, 1)
    return None

def get_direct_image_url(url):
    """将Reddit/Imgur等链接转换为直接图片URL"""
    if not url:
        return None

    # 已经是直接图片链接
    if url.endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp')):
        return url

    # Imgur链接处理
    if 'imgur.com' in url:
        # imgur.com/abc123 -> i.imgur.com/abc123.jpg
        if '/gallery/' in url or '/a/' in url:
            return None  # 跳过画廊和相册

        # 提取图片ID
        imgur_id = url.split('/')[-1].split('.')[0]
        if imgur_id and len(imgur_id) > 3:
            return f"https://i.imgur.com/{imgur_id}.jpg"

    # Reddit自己的图片服务
    if 'i.redd.it' in url:
        return url

    # Reddit preview链接
    if 'preview.redd.it' in url:
        return url

    # 其他可能的图片链接
    if any(domain in url for domain in ['i.imgur.com', 'imgur.io', 'gyazo.com']):
        return url

    return None

def download_and_split_image(url, post_id, output_dir="dataset"):
    """下载图片并分割"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30, stream=True)
        response.raise_for_status()
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存原始图片
        original_path = os.path.join(output_dir, f"{post_id}_original.jpg")
        with open(original_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        # 使用PIL打开图片
        with Image.open(original_path) as img:
            width, height = img.size
            
            # 分割图片
            mid_width = width // 2
            
            # 左半部分
            before_img = img.crop((0, 0, mid_width, height))
            before_path = os.path.join(output_dir, f"{post_id}_before.jpg")
            before_img.save(before_path, 'JPEG', quality=90)
            
            # 右半部分
            after_img = img.crop((mid_width, 0, width, height))
            after_path = os.path.join(output_dir, f"{post_id}_after.jpg")
            after_img.save(after_path, 'JPEG', quality=90)
        
        # 删除原始图片
        os.remove(original_path)
        
        return before_path, after_path
        
    except Exception as e:
        print(f"下载或分割图片失败: {e}")
        return None, None

def main():
    """主函数 - 多策略抓取"""
    print("Reddit Progress Pics爬虫")
    print("=" * 60)

    # 获取Reddit数据
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }

    collected_data = []
    output_dir = "dataset"
    os.makedirs(output_dir, exist_ok=True)

    collected = 0
    max_posts = 5000  # 5000条数据
    total_processed = 0   # 总处理的帖子数
    skipped_no_stats = 0  # 跳过：无统计信息
    skipped_no_image = 0  # 跳过：无有效图片
    skipped_download_fail = 0  # 跳过：下载失败
    processed_ids = set()  # 用于去重的ID集合

    # 多种排序策略，确保获取足够数据
    sort_strategies = [
        ("top", "all", "所有时间最热"),
        ("hot", None, "当前热门"),
        ("new", None, "最新帖子"),
        ("top", "year", "年度最热"),
        ("top", "month", "月度最热"),
        ("top", "week", "周度最热"),
        ("rising", None, "上升趋势"),
    ]

    try:
        print("开始多策略动态抓取Reddit数据...")

        for strategy_index, (sort_type, time_filter, description) in enumerate(sort_strategies):
            if collected >= max_posts:
                break

            print(f"\n{'='*60}")
            print(f"策略 {strategy_index + 1}/{len(sort_strategies)}: {description}")
            print(f"当前已收集: {collected}/{max_posts}")
            print(f"{'='*60}")

            after = None
            page_count = 0
            strategy_collected = 0

            # 每个策略最多抓取50页，避免无限循环
            while collected < max_posts and page_count < 50:
                page_count += 1

                # 构建URL
                base_url = f"https://www.reddit.com/r/progresspics/{sort_type}/.json"
                params = {"limit": 100}  # Reddit API最大限制
                if time_filter:
                    params["t"] = time_filter
                if after:
                    params["after"] = after

                # 构建完整URL
                url_params = "&".join([f"{k}={v}" for k, v in params.items()])
                url = f"{base_url}?{url_params}"

                print(f"\n{description} - 第{page_count}页 (策略收集: {strategy_collected}, 总收集: {collected}/{max_posts})")
                print(f"请求URL: {url}")

                try:
                    response = requests.get(url, headers=headers, timeout=30)
                    response.raise_for_status()

                    data = response.json()
                    posts = data.get('data', {}).get('children', [])
                    after = data.get('data', {}).get('after')  # 获取下一页的标识

                    print(f"获取到 {len(posts)} 个帖子")

                    if not posts:
                        print("没有更多帖子，切换到下一个策略")
                        break

                    # 处理当前页的帖子
                    for i, post_data in enumerate(posts):
                        total_processed += 1

                        if collected >= max_posts:
                            print(f"已达到目标数量 {max_posts}，停止抓取")
                            break

                        post = post_data.get('data', {})
                        title = post.get('title', '')
                        image_url = post.get('url', '')
                        post_id = post.get('id', f'post_{page_count}_{i}')

                        # 去重检查
                        if post_id in processed_ids:
                            print(f"跳过重复帖子: {post_id}")
                            continue

                        processed_ids.add(post_id)

                        print(f"\n处理帖子 {total_processed} (收集: {collected+1}/{max_posts}): {title[:50]}...")

                        # 提取统计信息
                        stats = extract_stats(title)
                        if not stats:
                            skipped_no_stats += 1
                            # 每100个失败的标题输出一个用于调试
                            if skipped_no_stats % 100 == 1:
                                print(f"调试样本 - 无法提取统计信息的标题: {title}")
                            continue

                        print(f"统计信息: {stats}")

                        # 智能处理图片URL
                        direct_image_url = get_direct_image_url(image_url)
                        if not direct_image_url:
                            skipped_no_image += 1
                            print(f"无法获取有效图片链接，跳过。原URL: {image_url}")
                            continue

                        print(f"图片URL: {direct_image_url}")

                        # 下载并分割图片
                        before_path, after_path = download_and_split_image(direct_image_url, post_id, output_dir)

                        if before_path and after_path:
                            # 转换单位并计算BMI
                            height_m = convert_height_to_meters(stats['height'], stats.get('height_unit'))
                            start_weight_kg = convert_weight_to_kg(stats['start_weight'], stats.get('weight_unit'))
                            end_weight_kg = convert_weight_to_kg(stats['end_weight'], stats.get('weight_unit'))

                            start_bmi = calculate_bmi(start_weight_kg, height_m) if height_m else None
                            end_bmi = calculate_bmi(end_weight_kg, height_m) if height_m else None

                            # 保存JSON文件
                            json_data = {
                                "title": title,
                                "post_id": post_id,
                                "height": stats['height'],
                                "start_weight": stats['start_weight'],
                                "end_weight": stats['end_weight'],
                                "height_m": height_m,
                                "start_weight_kg": start_weight_kg,
                                "end_weight_kg": end_weight_kg,
                                "start_bmi": start_bmi,
                                "end_bmi": end_bmi,
                                "url": image_url,
                                "before_image_path": before_path,
                                "after_image_path": after_path
                            }

                            # 记录有效数据
                            data_record = {
                                "before_image_path": before_path,
                                "after_image_path": after_path,
                                "start_bmi": start_bmi,
                                "end_bmi": end_bmi
                            }

                            json_path = os.path.join(output_dir, f"{post_id}.json")
                            with open(json_path, 'w', encoding='utf-8') as f:
                                json.dump(json_data, f, indent=2, ensure_ascii=False)

                            collected_data.append(data_record)
                            collected += 1
                            strategy_collected += 1
                            print(f"✓ 成功收集 {collected}/{max_posts} (策略: {strategy_collected})")
                            print(f"  身高: {height_m}m, 体重: {start_weight_kg}kg→{end_weight_kg}kg")
                            print(f"  BMI: {start_bmi}→{end_bmi}")
                            print(f"  图片: {before_path}, {after_path}")

                            # 每收集100条数据保存一次汇总到项目根目录
                            if collected % 100 == 0:
                                summary_path = "summary.json"  # 保存到项目根目录
                                with open(summary_path, 'w', encoding='utf-8') as f:
                                    json.dump(collected_data, f, indent=2, ensure_ascii=False)
                                print(f"  >> 已保存 {collected} 条数据到根目录汇总文件")
                                print(f"  >> 统计: 处理{total_processed}, 成功{collected}, 无统计{skipped_no_stats}, 无图片{skipped_no_image}, 下载失败{skipped_download_fail}")
                        else:
                            skipped_download_fail += 1
                            print("✗ 图片下载失败")

                    # 如果没有下一页标识，停止当前策略
                    if not after:
                        print("已到达最后一页，切换到下一个策略")
                        break

                    # 添加延迟避免请求过快
                    print(f"等待2秒后继续下一页...")
                    time.sleep(2)

                except requests.RequestException as e:
                    print(f"请求失败: {e}")
                    print("等待5秒后重试...")
                    time.sleep(5)
                    continue

            print(f"策略 '{description}' 完成，收集了 {strategy_collected} 条新数据")

        # 保存最终汇总数据到项目根目录
        if collected_data:
            summary_path = "summary.json"  # 保存到项目根目录
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(collected_data, f, indent=2, ensure_ascii=False)

            print(f"\n汇总数据已保存到项目根目录: {summary_path}")
            print(f"总共收集了 {len(collected_data)} 条记录")

        print(f"\n爬取完成！成功收集了 {collected} 个帖子")
        print(f"数据已保存在: {output_dir}")
        print(f"共处理了 {page_count} 页数据")
        print(f"\n详细统计:")
        print(f"  总处理帖子: {total_processed}")
        print(f"  成功收集: {collected}")
        print(f"  跳过-无统计信息: {skipped_no_stats}")
        print(f"  跳过-无有效图片: {skipped_no_image}")
        print(f"  跳过-下载失败: {skipped_download_fail}")
        print(f"  成功率: {collected/total_processed*100:.1f}%" if total_processed > 0 else "  成功率: 0%")

    except Exception as e:
        print(f"爬取失败: {e}")
        # 即使出错也保存已收集的数据到项目根目录
        if collected_data:
            summary_path = "summary_partial.json"  # 保存到项目根目录
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(collected_data, f, indent=2, ensure_ascii=False)
            print(f"已保存部分数据到项目根目录: {summary_path} ({len(collected_data)} 条记录)")

if __name__ == "__main__":
    main()
